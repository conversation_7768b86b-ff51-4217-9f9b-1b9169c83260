"""
对战相关数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum as PyEnum
from typing import Dict, Any, Optional

from .base import Base


class BattleStatus(PyEnum):
    """对战状态"""
    WAITING = "waiting"      # 等待接受
    ACTIVE = "active"        # 进行中
    FINISHED = "finished"    # 已结束
    CANCELLED = "cancelled"  # 已取消


class BattleType(PyEnum):
    """对战类型"""
    PVP = "pvp"             # 玩家对战
    PVE = "pve"             # 对战NPC
    WILD = "wild"           # 野生宝可梦
    GYM = "gym"             # 道馆战
    ELITE_FOUR = "elite_four"  # 四天王
    CHAMPION = "champion"    # 冠军战


class Battle(Base):
    """对战记录"""
    __tablename__ = "battles"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 对战基本信息
    battle_type = Column(Enum(BattleType), default=BattleType.PVP)
    status = Column(Enum(BattleStatus), default=BattleStatus.WAITING)
    
    # 参战者
    challenger_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    defender_id = Column(Integer, ForeignKey("users.id"))  # PVE时可能为空
    winner_id = Column(Integer, ForeignKey("users.id"))
    
    # 对战设置
    battle_format = Column(String(20), default="single")  # single, double, multi
    max_pokemon = Column(Integer, default=6)  # 最大宝可梦数量
    level_cap = Column(Integer)  # 等级限制
    
    # 对战状态
    current_turn = Column(Integer, default=1)
    active_pokemon = Column(JSON, default=dict)  # 当前出战的宝可梦
    field_conditions = Column(JSON, default=dict)  # 场地状态
    weather = Column(String(20))  # 天气
    
    # 时间记录
    created_at = Column(DateTime, default=func.now())
    started_at = Column(DateTime)
    ended_at = Column(DateTime)
    last_action_at = Column(DateTime, default=func.now())
    
    # 其他信息
    battle_log = Column(Text)  # 对战日志
    metadata = Column(JSON, default=dict)  # 额外数据
    
    # 关系
    challenger = relationship("User", foreign_keys=[challenger_id], back_populates="battles_as_challenger")
    defender = relationship("User", foreign_keys=[defender_id], back_populates="battles_as_defender")
    winner = relationship("User", foreign_keys=[winner_id])
    participants = relationship("BattleParticipant", back_populates="battle", cascade="all, delete-orphan")
    turns = relationship("BattleTurn", back_populates="battle", cascade="all, delete-orphan")
    pokemon_instances = relationship("Pokemon", back_populates="battle")
    
    @property
    def is_active(self) -> bool:
        """是否正在进行"""
        return self.status == BattleStatus.ACTIVE
    
    @property
    def duration(self) -> Optional[int]:
        """对战持续时间（秒）"""
        if self.started_at and self.ended_at:
            return int((self.ended_at - self.started_at).total_seconds())
        return None
    
    def __repr__(self):
        return f"<Battle(id={self.id}, type={self.battle_type.value}, status={self.status.value})>"


class BattleParticipant(Base):
    """对战参与者"""
    __tablename__ = "battle_participants"
    
    id = Column(Integer, primary_key=True, index=True)
    battle_id = Column(Integer, ForeignKey("battles.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # 参与信息
    team_position = Column(Integer)  # 队伍位置 (1或2)
    is_ready = Column(Boolean, default=False)  # 是否准备就绪
    
    # 队伍信息
    team_pokemon = Column(JSON, default=list)  # 参战宝可梦ID列表
    active_pokemon_id = Column(Integer)  # 当前出战宝可梦
    
    # 统计信息
    pokemon_fainted = Column(Integer, default=0)  # 倒下的宝可梦数量
    damage_dealt = Column(Integer, default=0)  # 造成的伤害
    damage_taken = Column(Integer, default=0)  # 受到的伤害
    
    # 时间戳
    joined_at = Column(DateTime, default=func.now())
    
    # 关系
    battle = relationship("Battle", back_populates="participants")
    user = relationship("User")
    
    def __repr__(self):
        return f"<BattleParticipant(battle_id={self.battle_id}, user_id={self.user_id})>"


class BattleTurn(Base):
    """对战回合记录"""
    __tablename__ = "battle_turns"
    
    id = Column(Integer, primary_key=True, index=True)
    battle_id = Column(Integer, ForeignKey("battles.id"), nullable=False)
    turn_number = Column(Integer, nullable=False)
    
    # 行动信息
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    pokemon_id = Column(Integer, ForeignKey("user_pokemon.id"), nullable=False)
    action_type = Column(String(20), nullable=False)  # move, switch, item, forfeit
    
    # 行动详情
    move_id = Column(Integer, ForeignKey("pokemon_moves.id"))  # 使用的技能
    target_pokemon_id = Column(Integer, ForeignKey("user_pokemon.id"))  # 目标宝可梦
    item_id = Column(Integer, ForeignKey("items.id"))  # 使用的道具
    switch_to_pokemon_id = Column(Integer, ForeignKey("user_pokemon.id"))  # 替换的宝可梦
    
    # 结果
    damage_dealt = Column(Integer, default=0)
    critical_hit = Column(Boolean, default=False)
    effectiveness = Column(Float, default=1.0)  # 效果倍率
    status_inflicted = Column(String(20))  # 造成的状态异常
    
    # 其他信息
    priority = Column(Integer, default=0)  # 行动优先度
    success = Column(Boolean, default=True)  # 行动是否成功
    description = Column(Text)  # 行动描述
    
    # 时间戳
    created_at = Column(DateTime, default=func.now())
    
    # 关系
    battle = relationship("Battle", back_populates="turns")
    user = relationship("User")
    pokemon = relationship("UserPokemon", foreign_keys=[pokemon_id])
    move = relationship("PokemonMove")
    item = relationship("Item")
    target_pokemon = relationship("UserPokemon", foreign_keys=[target_pokemon_id])
    switch_to_pokemon = relationship("UserPokemon", foreign_keys=[switch_to_pokemon_id])
    
    def __repr__(self):
        return f"<BattleTurn(battle_id={self.battle_id}, turn={self.turn_number}, action={self.action_type})>"
