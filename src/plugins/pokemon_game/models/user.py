"""
用户相关数据模型
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, ForeignKey, Text, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional

from .base import Base


class User(Base):
    """用户模型"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    qq_id = Column(String(20), unique=True, index=True, nullable=False)
    username = Column(String(50), nullable=False)
    
    # 游戏数据
    level = Column(Integer, default=1)
    exp = Column(Integer, default=0)
    money = Column(Integer, default=1000)  # 游戏币
    
    # 游戏进度
    badges = Column(JSON, default=list)  # 徽章列表
    story_progress = Column(Integer, default=0)  # 剧情进度
    
    # 统计数据
    total_battles = Column(Integer, default=0)
    wins = Column(Integer, default=0)
    losses = Column(Integer, default=0)
    pokemon_caught = Column(Integer, default=0)
    
    # 设置
    settings = Column(JSON, default=dict)  # 用户设置
    
    # 时间戳
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_active = Column(DateTime, default=func.now())
    
    # 关系
    pokemon = relationship("UserPokemon", back_populates="owner", cascade="all, delete-orphan")
    items = relationship("UserItem", back_populates="owner", cascade="all, delete-orphan")
    battles_as_challenger = relationship("Battle", foreign_keys="Battle.challenger_id", back_populates="challenger")
    battles_as_defender = relationship("Battle", foreign_keys="Battle.defender_id", back_populates="defender")
    
    @property
    def win_rate(self) -> float:
        """胜率"""
        if self.total_battles == 0:
            return 0.0
        return self.wins / self.total_battles
    
    @property
    def team_pokemon(self) -> list:
        """队伍中的宝可梦"""
        return [p for p in self.pokemon if p.in_team]
    
    def __repr__(self):
        return f"<User(qq_id={self.qq_id}, username={self.username}, level={self.level})>"


class UserPokemon(Base):
    """用户拥有的宝可梦"""
    __tablename__ = "user_pokemon"
    
    id = Column(Integer, primary_key=True, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    species_id = Column(Integer, ForeignKey("pokemon_species.id"), nullable=False)
    
    # 基础信息
    nickname = Column(String(50))  # 昵称
    level = Column(Integer, default=1)
    exp = Column(Integer, default=0)
    gender = Column(String(10))  # male, female, genderless
    nature = Column(String(20))  # 性格
    ability = Column(String(50))  # 特性
    
    # 个体值 (0-31)
    iv_hp = Column(Integer, default=0)
    iv_attack = Column(Integer, default=0)
    iv_defense = Column(Integer, default=0)
    iv_sp_attack = Column(Integer, default=0)
    iv_sp_defense = Column(Integer, default=0)
    iv_speed = Column(Integer, default=0)
    
    # 努力值 (0-252, 总和不超过510)
    ev_hp = Column(Integer, default=0)
    ev_attack = Column(Integer, default=0)
    ev_defense = Column(Integer, default=0)
    ev_sp_attack = Column(Integer, default=0)
    ev_sp_defense = Column(Integer, default=0)
    ev_speed = Column(Integer, default=0)
    
    # 技能 (最多4个)
    moves = Column(JSON, default=list)  # 技能ID列表
    
    # 状态
    current_hp = Column(Integer)  # 当前HP
    status = Column(String(20))  # 状态异常
    in_team = Column(Boolean, default=False)  # 是否在队伍中
    is_shiny = Column(Boolean, default=False)  # 是否异色
    
    # 其他
    held_item_id = Column(Integer, ForeignKey("items.id"))  # 携带道具
    friendship = Column(Integer, default=70)  # 亲密度
    
    # 时间戳
    caught_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    owner = relationship("User", back_populates="pokemon")
    species = relationship("PokemonSpecies")
    held_item = relationship("Item")
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        return self.nickname or self.species.name
    
    @property
    def max_hp(self) -> int:
        """最大HP计算"""
        base_hp = self.species.base_hp
        return ((2 * base_hp + self.iv_hp + self.ev_hp // 4) * self.level // 100) + self.level + 10
    
    def __repr__(self):
        return f"<UserPokemon(id={self.id}, species={self.species.name}, level={self.level})>"


class UserItem(Base):
    """用户拥有的道具"""
    __tablename__ = "user_items"
    
    id = Column(Integer, primary_key=True, index=True)
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    item_id = Column(Integer, ForeignKey("items.id"), nullable=False)
    quantity = Column(Integer, default=1)
    
    # 时间戳
    obtained_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # 关系
    owner = relationship("User", back_populates="items")
    item = relationship("Item")
    
    def __repr__(self):
        return f"<UserItem(item={self.item.name}, quantity={self.quantity})>"
