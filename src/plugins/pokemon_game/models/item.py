"""
道具相关数据模型
"""

from sqlalchemy import Column, Integer, String, Boolean, ForeignKey, Text, JSON, Enum
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from typing import Dict, Any

from .base import Base


class ItemCategory(PyEnum):
    """道具分类"""
    POKEBALL = "pokeball"           # 精灵球
    MEDICINE = "medicine"           # 药品
    BERRY = "berry"                 # 树果
    TM = "tm"                      # 技能机器
    EVOLUTION = "evolution"         # 进化道具
    BATTLE_ITEM = "battle_item"     # 对战道具
    HELD_ITEM = "held_item"         # 携带道具
    KEY_ITEM = "key_item"           # 重要道具
    MISC = "misc"                   # 其他


class Item(Base):
    """道具"""
    __tablename__ = "items"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)
    name_zh = Column(String(50), nullable=False)  # 中文名
    
    # 基本信息
    category = Column(Enum(ItemCategory), nullable=False)
    description = Column(Text)  # 描述
    flavor_text = Column(Text)  # 风味文本
    
    # 属性
    buy_price = Column(Integer, default=0)  # 购买价格
    sell_price = Column(Integer, default=0)  # 出售价格
    is_consumable = Column(Boolean, default=True)  # 是否消耗品
    is_holdable = Column(Boolean, default=False)  # 是否可携带
    is_tradeable = Column(Boolean, default=True)  # 是否可交易
    
    # 效果相关
    effect_id = Column(Integer)  # 效果ID
    effect_data = Column(JSON, default=dict)  # 效果数据
    
    # 使用限制
    usable_in_battle = Column(Boolean, default=False)  # 战斗中可用
    usable_on_pokemon = Column(Boolean, default=False)  # 可对宝可梦使用
    target_type = Column(String(20))  # 目标类型
    
    # 其他信息
    sprite_url = Column(String(200))  # 图标URL
    generation = Column(Integer, default=9)  # 世代
    rarity = Column(Integer, default=1)  # 稀有度 (1-5)
    
    # 特殊属性 (针对不同类型道具)
    pokeball_catch_rate = Column(Integer)  # 精灵球捕获率加成
    medicine_heal_amount = Column(Integer)  # 药品回复量
    tm_move_id = Column(Integer, ForeignKey("pokemon_moves.id"))  # 技能机器对应技能
    
    # 关系
    tm_move = relationship("PokemonMove")
    
    @property
    def is_pokeball(self) -> bool:
        """是否为精灵球"""
        return self.category == ItemCategory.POKEBALL
    
    @property
    def is_medicine(self) -> bool:
        """是否为药品"""
        return self.category == ItemCategory.MEDICINE
    
    @property
    def is_tm(self) -> bool:
        """是否为技能机器"""
        return self.category == ItemCategory.TM
    
    def __repr__(self):
        return f"<Item(name={self.name_zh}, category={self.category.value})>"
