"""
宝可梦相关数据模型
"""

from sqlalchemy import Column, Integer, String, Float, Boolean, ForeignKey, Text, JSON, Table
from sqlalchemy.orm import relationship
from typing import List, Dict, Any

from .base import Base

# 宝可梦类型关联表
pokemon_types = Table(
    'pokemon_species_types',
    Base.metadata,
    Column('species_id', Integer, ForeignKey('pokemon_species.id'), primary_key=True),
    Column('type_id', Integer, ForeignKey('pokemon_types.id'), primary_key=True),
    Column('slot', Integer)  # 类型槽位 (1或2)
)

# 宝可梦技能关联表
pokemon_moves = Table(
    'pokemon_species_moves',
    Base.metadata,
    Column('species_id', Integer, ForeignKey('pokemon_species.id'), primary_key=True),
    Column('move_id', Integer, ForeignKey('pokemon_moves.id'), primary_key=True),
    Column('learn_method', String(20)),  # level_up, tm, egg, tutor
    Column('level', Integer),  # 学会等级
    Column('order', Integer)  # 排序
)


class PokemonType(Base):
    """宝可梦属性类型"""
    __tablename__ = "pokemon_types"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(20), unique=True, nullable=False)
    name_zh = Column(String(20), nullable=False)  # 中文名
    color = Column(String(7))  # 颜色代码
    
    # 属性相克关系 (JSON格式存储)
    effectiveness = Column(JSON, default=dict)  # 对其他属性的克制关系
    
    def __repr__(self):
        return f"<PokemonType(name={self.name}, name_zh={self.name_zh})>"


class PokemonSpecies(Base):
    """宝可梦种族"""
    __tablename__ = "pokemon_species"
    
    id = Column(Integer, primary_key=True, index=True)  # 全国图鉴编号
    name = Column(String(50), nullable=False)
    name_zh = Column(String(50), nullable=False)  # 中文名
    
    # 基础属性
    base_hp = Column(Integer, nullable=False)
    base_attack = Column(Integer, nullable=False)
    base_defense = Column(Integer, nullable=False)
    base_sp_attack = Column(Integer, nullable=False)
    base_sp_defense = Column(Integer, nullable=False)
    base_speed = Column(Integer, nullable=False)
    
    # 其他属性
    height = Column(Float)  # 身高 (米)
    weight = Column(Float)  # 体重 (千克)
    capture_rate = Column(Integer, default=45)  # 捕获率
    base_experience = Column(Integer, default=100)  # 基础经验值
    growth_rate = Column(String(20), default="medium")  # 成长速度
    
    # 进化相关
    evolution_chain_id = Column(Integer)
    evolves_from_species_id = Column(Integer, ForeignKey("pokemon_species.id"))
    evolution_trigger = Column(String(20))  # level, stone, trade, friendship等
    evolution_level = Column(Integer)  # 进化等级
    evolution_item_id = Column(Integer, ForeignKey("items.id"))  # 进化道具
    evolution_conditions = Column(JSON, default=dict)  # 其他进化条件
    
    # 特性
    abilities = Column(JSON, default=list)  # 普通特性列表
    hidden_ability = Column(String(50))  # 隐藏特性
    
    # 蛋组和性别
    egg_groups = Column(JSON, default=list)  # 蛋组
    gender_rate = Column(Integer, default=4)  # 性别比例 (-1=无性别, 0=全雄, 8=全雌)
    
    # 其他信息
    generation = Column(Integer, default=9)  # 世代
    is_legendary = Column(Boolean, default=False)  # 传说宝可梦
    is_mythical = Column(Boolean, default=False)  # 幻之宝可梦
    is_baby = Column(Boolean, default=False)  # 幼年宝可梦
    
    # 图片和描述
    sprite_url = Column(String(200))  # 精灵图URL
    description = Column(Text)  # 描述
    
    # 关系
    types = relationship("PokemonType", secondary=pokemon_types, backref="pokemon_species")
    moves = relationship("PokemonMove", secondary=pokemon_moves, backref="pokemon_species")
    evolves_from = relationship("PokemonSpecies", remote_side=[id])
    evolution_item = relationship("Item")
    
    @property
    def base_stat_total(self) -> int:
        """种族值总和"""
        return (self.base_hp + self.base_attack + self.base_defense + 
                self.base_sp_attack + self.base_sp_defense + self.base_speed)
    
    @property
    def type_names(self) -> List[str]:
        """属性名称列表"""
        return [t.name_zh for t in self.types]
    
    def __repr__(self):
        return f"<PokemonSpecies(id={self.id}, name={self.name_zh})>"


class PokemonMove(Base):
    """宝可梦技能"""
    __tablename__ = "pokemon_moves"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False)
    name_zh = Column(String(50), nullable=False)  # 中文名
    
    # 技能属性
    type_id = Column(Integer, ForeignKey("pokemon_types.id"), nullable=False)
    category = Column(String(20), nullable=False)  # physical, special, status
    power = Column(Integer)  # 威力
    accuracy = Column(Integer)  # 命中率
    pp = Column(Integer, nullable=False)  # PP
    priority = Column(Integer, default=0)  # 优先度
    
    # 效果
    effect_id = Column(Integer)  # 效果ID
    effect_chance = Column(Integer)  # 效果发动概率
    target = Column(String(20))  # 目标类型
    
    # 其他信息
    generation = Column(Integer, default=9)
    description = Column(Text)  # 技能描述
    
    # 关系
    type = relationship("PokemonType")
    
    def __repr__(self):
        return f"<PokemonMove(name={self.name_zh}, type={self.type.name_zh}, power={self.power})>"


class Pokemon(Base):
    """宝可梦实例 (用于对战等临时数据)"""
    __tablename__ = "pokemon_instances"
    
    id = Column(Integer, primary_key=True, index=True)
    user_pokemon_id = Column(Integer, ForeignKey("user_pokemon.id"), nullable=False)
    
    # 当前状态
    current_hp = Column(Integer, nullable=False)
    status_condition = Column(String(20))  # 状态异常
    
    # 临时修正值
    stat_stages = Column(JSON, default=dict)  # 能力等级变化
    
    # 对战相关
    battle_id = Column(Integer, ForeignKey("battles.id"))
    position = Column(Integer)  # 在队伍中的位置
    
    # 关系
    user_pokemon = relationship("UserPokemon")
    battle = relationship("Battle")
    
    def __repr__(self):
        return f"<Pokemon(id={self.id}, hp={self.current_hp})>"
