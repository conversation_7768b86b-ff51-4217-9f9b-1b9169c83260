"""
道具相关命令处理
"""

from nonebot import on_command
from nonebot.adapters.onebot.v11 import Bo<PERSON>, Event
from nonebot.params import CommandArg
from nonebot.typing import T_State

from ..core.user_service import UserService
from ..core.item_service import ItemService
from ..utils.message_builder import MessageBuilder

# 命令定义
items_cmd = on_command("pokemon items", aliases={"道具", "背包"}, priority=10, block=True)
shop_cmd = on_command("pokemon shop", aliases={"商店"}, priority=10, block=True)
buy_cmd = on_command("pokemon buy", aliases={"购买"}, priority=10, block=True)
use_item_cmd = on_command("pokemon use", aliases={"使用道具"}, priority=10, block=True)


@items_cmd.handle()
async def handle_items(bot: Bot, event: Event, state: T_State):
    """处理查看道具命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await items_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        user_items = await UserService.get_user_items(user.id)
        
        if not user_items:
            await items_cmd.send("您的背包是空的。使用 /pokemon shop 查看商店。")
            return
        
        message = MessageBuilder.build_items_info(user_items)
        await items_cmd.send(message)
        
    except Exception as e:
        await items_cmd.send(f"查看道具时发生错误：{str(e)}")


@shop_cmd.handle()
async def handle_shop(bot: Bot, event: Event, state: T_State):
    """处理查看商店命令"""
    try:
        shop_items = await ItemService.get_shop_items()
        message = MessageBuilder.build_shop_info(shop_items)
        await shop_cmd.send(message)
        
    except Exception as e:
        await shop_cmd.send(f"查看商店时发生错误：{str(e)}")


@buy_cmd.handle()
async def handle_buy(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理购买道具命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await buy_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 解析参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await buy_cmd.send("请指定要购买的道具，例如：/pokemon buy 精灵球 5")
            return
        
        parts = arg_text.split()
        item_name = parts[0]
        quantity = 1
        
        if len(parts) > 1:
            try:
                quantity = int(parts[1])
                if quantity <= 0:
                    await buy_cmd.send("购买数量必须大于0。")
                    return
            except ValueError:
                await buy_cmd.send("购买数量必须是数字。")
                return
        
        # 购买道具
        result = await ItemService.buy_item(user.id, item_name, quantity)
        
        if result["success"]:
            message = MessageBuilder.build_buy_success(result)
            await buy_cmd.send(message)
        else:
            await buy_cmd.send(result["message"])
            
    except Exception as e:
        await buy_cmd.send(f"购买道具时发生错误：{str(e)}")


@use_item_cmd.handle()
async def handle_use_item(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理使用道具命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await use_item_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 解析参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await use_item_cmd.send("请指定要使用的道具和目标，例如：/pokemon use 伤药 1")
            return
        
        parts = arg_text.split()
        if len(parts) < 2:
            await use_item_cmd.send("请指定道具名称和目标宝可梦ID。")
            return
        
        item_name = parts[0]
        try:
            pokemon_id = int(parts[1])
        except ValueError:
            await use_item_cmd.send("宝可梦ID必须是数字。")
            return
        
        # 使用道具
        result = await ItemService.use_item_on_pokemon(user.id, item_name, pokemon_id)
        
        if result["success"]:
            message = MessageBuilder.build_use_item_result(result)
            await use_item_cmd.send(message)
        else:
            await use_item_cmd.send(result["message"])
            
    except Exception as e:
        await use_item_cmd.send(f"使用道具时发生错误：{str(e)}")
