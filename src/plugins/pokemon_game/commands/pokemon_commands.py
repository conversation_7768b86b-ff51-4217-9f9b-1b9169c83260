"""
宝可梦相关命令处理
"""

import random
from nonebot import on_command
from nonebot.adapters.onebot.v11 import Bot, Event, MessageSegment
from nonebot.params import <PERSON>Arg, Arg
from nonebot.typing import T_State
from nonebot.matcher import Matcher

from ..core.user_service import UserService
from ..core.pokemon_service import PokemonService
from ..utils.message_builder import MessageBuilder

# 命令定义
catch_cmd = on_command("pokemon catch", aliases={"捕捉", "抓宝可梦"}, priority=10, block=True)
train_cmd = on_command("pokemon train", aliases={"训练"}, priority=10, block=True)
evolve_cmd = on_command("pokemon evolve", aliases={"进化"}, priority=10, block=True)
team_cmd = on_command("pokemon team", aliases={"队伍", "查看队伍"}, priority=10, block=True)
box_cmd = on_command("pokemon box", aliases={"盒子", "宝可梦盒子"}, priority=10, block=True)
add_team_cmd = on_command("pokemon add", aliases={"加入队伍"}, priority=10, block=True)
remove_team_cmd = on_command("pokemon remove", aliases={"移出队伍"}, priority=10, block=True)


@catch_cmd.handle()
async def handle_catch(bot: Bot, event: Event, state: T_State):
    """处理捕捉命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await catch_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 随机遇到野生宝可梦 (1-4号宝可梦)
        wild_species_id = random.randint(1, 4)
        
        # 检查是否有精灵球
        user_items = await UserService.get_user_items(user.id)
        pokeball_item = next((item for item in user_items if item.item_id == 1), None)
        
        if not pokeball_item or pokeball_item.quantity <= 0:
            await catch_cmd.send("您没有精灵球了！请先去商店购买。")
            return
        
        # 尝试捕捉
        result = await PokemonService.catch_pokemon(user.id, wild_species_id, 1)
        
        if result["success"]:
            message = MessageBuilder.build_catch_success(result["pokemon"])
            await catch_cmd.send(message)
        else:
            await catch_cmd.send(result["message"])
            
    except Exception as e:
        await catch_cmd.send(f"捕捉时发生错误：{str(e)}")


@train_cmd.handle()
async def handle_train(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理训练命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await train_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 解析参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await train_cmd.send("请指定要训练的宝可梦ID，例如：/pokemon train 1")
            return
        
        try:
            pokemon_id = int(arg_text)
        except ValueError:
            await train_cmd.send("宝可梦ID必须是数字。")
            return
        
        # 检查宝可梦是否属于用户
        user_pokemon = await UserService.get_user_pokemon(user.id)
        target_pokemon = next((p for p in user_pokemon if p.id == pokemon_id), None)
        
        if not target_pokemon:
            await train_cmd.send("找不到指定的宝可梦。")
            return
        
        # 训练宝可梦
        result = await PokemonService.train_pokemon(pokemon_id)
        
        if result["success"]:
            message = MessageBuilder.build_train_result(target_pokemon, result)
            await train_cmd.send(message)
        else:
            await train_cmd.send(result["message"])
            
    except Exception as e:
        await train_cmd.send(f"训练时发生错误：{str(e)}")


@evolve_cmd.handle()
async def handle_evolve(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理进化命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await evolve_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 解析参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await evolve_cmd.send("请指定要进化的宝可梦ID，例如：/pokemon evolve 1")
            return
        
        try:
            pokemon_id = int(arg_text)
        except ValueError:
            await evolve_cmd.send("宝可梦ID必须是数字。")
            return
        
        # 检查宝可梦是否属于用户
        user_pokemon = await UserService.get_user_pokemon(user.id)
        target_pokemon = next((p for p in user_pokemon if p.id == pokemon_id), None)
        
        if not target_pokemon:
            await evolve_cmd.send("找不到指定的宝可梦。")
            return
        
        # 尝试进化
        result = await PokemonService.evolve_pokemon(pokemon_id)
        
        if result["success"]:
            message = MessageBuilder.build_evolution_result(result)
            await evolve_cmd.send(message)
        else:
            await evolve_cmd.send(result["message"])
            
    except Exception as e:
        await evolve_cmd.send(f"进化时发生错误：{str(e)}")


@team_cmd.handle()
async def handle_team(bot: Bot, event: Event, state: T_State):
    """处理查看队伍命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await team_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        team_pokemon = await UserService.get_user_pokemon(user.id, in_team_only=True)
        
        if not team_pokemon:
            await team_cmd.send("您的队伍中还没有宝可梦。使用 /pokemon add <ID> 将宝可梦加入队伍。")
            return
        
        message = MessageBuilder.build_team_info(team_pokemon)
        await team_cmd.send(message)
        
    except Exception as e:
        await team_cmd.send(f"查看队伍时发生错误：{str(e)}")


@box_cmd.handle()
async def handle_box(bot: Bot, event: Event, state: T_State):
    """处理查看盒子命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await box_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        all_pokemon = await UserService.get_user_pokemon(user.id)
        
        if not all_pokemon:
            await box_cmd.send("您还没有任何宝可梦。使用 /pokemon catch 去捕捉一些吧！")
            return
        
        message = MessageBuilder.build_box_info(all_pokemon)
        await box_cmd.send(message)
        
    except Exception as e:
        await box_cmd.send(f"查看盒子时发生错误：{str(e)}")


@add_team_cmd.handle()
async def handle_add_team(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理加入队伍命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await add_team_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 解析参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await add_team_cmd.send("请指定要加入队伍的宝可梦ID，例如：/pokemon add 1")
            return
        
        try:
            pokemon_id = int(arg_text)
        except ValueError:
            await add_team_cmd.send("宝可梦ID必须是数字。")
            return
        
        # 检查宝可梦是否属于用户
        user_pokemon = await UserService.get_user_pokemon(user.id)
        target_pokemon = next((p for p in user_pokemon if p.id == pokemon_id), None)
        
        if not target_pokemon:
            await add_team_cmd.send("找不到指定的宝可梦。")
            return
        
        if target_pokemon.in_team:
            await add_team_cmd.send(f"{target_pokemon.display_name}已经在队伍中了。")
            return
        
        # 加入队伍
        result = await PokemonService.add_to_team(pokemon_id)
        await add_team_cmd.send(result["message"])
        
    except Exception as e:
        await add_team_cmd.send(f"加入队伍时发生错误：{str(e)}")


@remove_team_cmd.handle()
async def handle_remove_team(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理移出队伍命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await remove_team_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 解析参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await remove_team_cmd.send("请指定要移出队伍的宝可梦ID，例如：/pokemon remove 1")
            return
        
        try:
            pokemon_id = int(arg_text)
        except ValueError:
            await remove_team_cmd.send("宝可梦ID必须是数字。")
            return
        
        # 检查宝可梦是否属于用户
        user_pokemon = await UserService.get_user_pokemon(user.id)
        target_pokemon = next((p for p in user_pokemon if p.id == pokemon_id), None)
        
        if not target_pokemon:
            await remove_team_cmd.send("找不到指定的宝可梦。")
            return
        
        if not target_pokemon.in_team:
            await remove_team_cmd.send(f"{target_pokemon.display_name}不在队伍中。")
            return
        
        # 移出队伍
        result = await PokemonService.remove_from_team(pokemon_id)
        await remove_team_cmd.send(result["message"])
        
    except Exception as e:
        await remove_team_cmd.send(f"移出队伍时发生错误：{str(e)}")
