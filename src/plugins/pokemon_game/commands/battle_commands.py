"""
对战相关命令处理
"""

from nonebot import on_command
from nonebot.adapters.onebot.v11 import Bot, Event, MessageSegment, GroupMessageEvent
from nonebot.params import CommandArg
from nonebot.typing import T_State

from ..core.user_service import UserService
from ..core.battle_service import BattleService
from ..models import BattleType
from ..utils.message_builder import MessageBuilder

# 全局对战状态存储 (实际应用中应该使用Redis等)
pending_battles = {}  # {user_id: battle_id}
active_battles = {}   # {user_id: battle_id}

# 命令定义
battle_cmd = on_command("pokemon battle", aliases={"对战", "挑战"}, priority=10, block=True)
accept_cmd = on_command("pokemon accept", aliases={"接受对战", "接受"}, priority=10, block=True)
decline_cmd = on_command("pokemon decline", aliases={"拒绝对战", "拒绝"}, priority=10, block=True)
move_cmd = on_command("pokemon move", aliases={"使用技能", "技能"}, priority=10, block=True)
battle_status_cmd = on_command("pokemon status", aliases={"对战状态", "状态"}, priority=10, block=True)


@battle_cmd.handle()
async def handle_battle(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理对战命令"""
    if not isinstance(event, GroupMessageEvent):
        await battle_cmd.send("对战功能只能在群聊中使用。")
        return
    
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        if not user:
            await battle_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 检查是否已在对战中
        if user_id in active_battles or user_id in pending_battles:
            await battle_cmd.send("您已经在对战中或有待处理的对战邀请。")
            return
        
        # 解析目标用户
        message = event.get_message()
        at_segments = [seg for seg in message if seg.type == "at"]
        
        if not at_segments:
            await battle_cmd.send("请@要挑战的用户，例如：/pokemon battle @用户名")
            return
        
        target_qq = str(at_segments[0].data["qq"])
        
        if target_qq == user_id:
            await battle_cmd.send("不能挑战自己！")
            return
        
        # 检查目标用户是否注册
        target_user = await UserService.get_user_by_qq(target_qq)
        if not target_user:
            await battle_cmd.send("对方还未注册游戏。")
            return
        
        # 检查目标用户是否已在对战中
        if target_qq in active_battles or target_qq in pending_battles:
            await battle_cmd.send("对方已经在对战中或有待处理的对战邀请。")
            return
        
        # 检查双方是否都有队伍
        challenger_team = await UserService.get_user_pokemon(user.id, in_team_only=True)
        defender_team = await UserService.get_user_pokemon(target_user.id, in_team_only=True)
        
        if not challenger_team:
            await battle_cmd.send("您的队伍中没有宝可梦，请先组建队伍。")
            return
        
        if not defender_team:
            await battle_cmd.send("对方的队伍中没有宝可梦。")
            return
        
        # 创建对战
        result = await BattleService.create_battle(user.id, target_user.id, BattleType.PVP)
        
        if result["success"]:
            battle_id = result["battle_id"]
            pending_battles[target_qq] = battle_id
            
            message = MessageBuilder.build_battle_invitation(user.username, target_user.username)
            await battle_cmd.send(message)
        else:
            await battle_cmd.send(result["message"])
            
    except Exception as e:
        await battle_cmd.send(f"发起对战时发生错误：{str(e)}")


@accept_cmd.handle()
async def handle_accept(bot: Bot, event: Event, state: T_State):
    """处理接受对战命令"""
    user_id = str(event.get_user_id())
    
    try:
        if user_id not in pending_battles:
            await accept_cmd.send("您没有待处理的对战邀请。")
            return
        
        battle_id = pending_battles[user_id]
        
        # 接受对战
        result = await BattleService.accept_battle(battle_id, int(user_id))
        
        if result["success"]:
            # 移动到活跃对战
            del pending_battles[user_id]
            
            # 获取对战信息
            battle_state = await BattleService.get_battle_state(battle_id)
            if battle_state:
                for participant in battle_state["participants"]:
                    active_battles[str(participant["user_id"])] = battle_id
            
            message = MessageBuilder.build_battle_start()
            await accept_cmd.send(message)
        else:
            await accept_cmd.send(result["message"])
            
    except Exception as e:
        await accept_cmd.send(f"接受对战时发生错误：{str(e)}")


@decline_cmd.handle()
async def handle_decline(bot: Bot, event: Event, state: T_State):
    """处理拒绝对战命令"""
    user_id = str(event.get_user_id())
    
    try:
        if user_id not in pending_battles:
            await decline_cmd.send("您没有待处理的对战邀请。")
            return
        
        battle_id = pending_battles[user_id]
        del pending_battles[user_id]
        
        await decline_cmd.send("您拒绝了对战邀请。")
        
    except Exception as e:
        await decline_cmd.send(f"拒绝对战时发生错误：{str(e)}")


@move_cmd.handle()
async def handle_move(bot: Bot, event: Event, state: T_State, args=CommandArg()):
    """处理使用技能命令"""
    user_id = str(event.get_user_id())
    
    try:
        if user_id not in active_battles:
            await move_cmd.send("您当前不在对战中。")
            return
        
        battle_id = active_battles[user_id]
        
        # 解析技能参数
        arg_text = args.extract_plain_text().strip()
        if not arg_text:
            await move_cmd.send("请指定要使用的技能，例如：/pokemon move 撞击")
            return
        
        # 简化处理：根据技能名称映射到ID
        move_mapping = {
            "撞击": 33,
            "拍击": 1,
            "火花": 52,
            "水枪": 55,
            "电击": 84
        }
        
        move_id = move_mapping.get(arg_text)
        if not move_id:
            available_moves = ", ".join(move_mapping.keys())
            await move_cmd.send(f"未知技能。可用技能：{available_moves}")
            return
        
        # 使用技能
        result = await BattleService.use_move(battle_id, int(user_id), move_id)
        
        if result["success"]:
            message = MessageBuilder.build_move_result(result)
            await move_cmd.send(message)
            
            # 检查对战是否结束
            if result.get("battle_ended"):
                winner_id = str(result["winner_id"])
                loser_id = str(result["loser_id"])
                
                # 清理对战状态
                if winner_id in active_battles:
                    del active_battles[winner_id]
                if loser_id in active_battles:
                    del active_battles[loser_id]
                
                # 更新统计
                await UserService.update_battle_stats(result["winner_id"], True)
                await UserService.update_battle_stats(result["loser_id"], False)
                
                end_message = MessageBuilder.build_battle_end(result["winner_id"], result["loser_id"])
                await move_cmd.send(end_message)
        else:
            await move_cmd.send(result["message"])
            
    except Exception as e:
        await move_cmd.send(f"使用技能时发生错误：{str(e)}")


@battle_status_cmd.handle()
async def handle_battle_status(bot: Bot, event: Event, state: T_State):
    """处理查看对战状态命令"""
    user_id = str(event.get_user_id())
    
    try:
        if user_id not in active_battles:
            await battle_status_cmd.send("您当前不在对战中。")
            return
        
        battle_id = active_battles[user_id]
        battle_state = await BattleService.get_battle_state(battle_id)
        
        if battle_state:
            message = MessageBuilder.build_battle_status(battle_state)
            await battle_status_cmd.send(message)
        else:
            await battle_status_cmd.send("无法获取对战状态。")
            
    except Exception as e:
        await battle_status_cmd.send(f"查看对战状态时发生错误：{str(e)}")
