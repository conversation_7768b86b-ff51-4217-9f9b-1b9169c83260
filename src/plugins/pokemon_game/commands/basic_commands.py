"""
基础命令处理
"""

from nonebot import on_command
from nonebot.adapters.onebot.v11 import Bot, Event, MessageSegment
from nonebot.params import CommandArg
from nonebot.typing import T_State

from ..core.user_service import UserService
from ..utils.message_builder import MessageBuilder

# 注册命令
register_cmd = on_command("pokemon register", aliases={"宝可梦注册", "注册"}, priority=10, block=True)
profile_cmd = on_command("pokemon profile", aliases={"宝可梦信息", "个人信息"}, priority=10, block=True)
help_cmd = on_command("pokemon help", aliases={"宝可梦帮助", "帮助"}, priority=10, block=True)


@register_cmd.handle()
async def handle_register(bot: Bot, event: Event, state: T_State):
    """处理注册命令"""
    user_id = str(event.get_user_id())
    user_info = await bot.get_stranger_info(user_id=int(user_id))
    username = user_info.get("nickname", f"用户{user_id}")
    
    try:
        user = await UserService.get_or_create_user(user_id, username)
        
        if user:
            message = MessageBuilder.build_register_success(user)
            await register_cmd.send(message)
        else:
            await register_cmd.send("注册失败，请稍后重试。")
            
    except Exception as e:
        await register_cmd.send(f"注册时发生错误：{str(e)}")


@profile_cmd.handle()
async def handle_profile(bot: Bot, event: Event, state: T_State):
    """处理个人信息命令"""
    user_id = str(event.get_user_id())
    
    try:
        user = await UserService.get_user_by_qq(user_id)
        
        if not user:
            await profile_cmd.send("您还未注册，请先使用 /pokemon register 注册。")
            return
        
        # 获取用户的宝可梦
        pokemon_list = await UserService.get_user_pokemon(user.id)
        team_pokemon = [p for p in pokemon_list if p.in_team]
        
        message = MessageBuilder.build_profile(user, team_pokemon, len(pokemon_list))
        await profile_cmd.send(message)
        
    except Exception as e:
        await profile_cmd.send(f"获取信息时发生错误：{str(e)}")


@help_cmd.handle()
async def handle_help(bot: Bot, event: Event, state: T_State):
    """处理帮助命令"""
    help_text = """
🎮 宝可梦游戏帮助

📋 基础命令：
• /pokemon register - 注册游戏账号
• /pokemon profile - 查看个人信息
• /pokemon team - 查看队伍

🎯 养成系统：
• /pokemon catch - 捕捉野生宝可梦
• /pokemon train <ID> - 训练宝可梦
• /pokemon evolve <ID> - 进化宝可梦
• /pokemon box - 查看宝可梦盒子
• /pokemon add <ID> - 将宝可梦加入队伍
• /pokemon remove <ID> - 将宝可梦移出队伍

🎒 道具系统：
• /pokemon items - 查看道具背包
• /pokemon shop - 查看商店
• /pokemon buy <道具名> [数量] - 购买道具

⚔️ 对战系统：
• /pokemon battle <@用户> - 发起PVP对战
• /pokemon accept - 接受对战邀请
• /pokemon decline - 拒绝对战邀请
• /pokemon move <技能名> - 在对战中使用技能

💡 提示：
- 每日可免费捕捉10只宝可梦
- 训练宝可梦可以提升等级和能力
- 队伍最多可容纳6只宝可梦
- 对战胜利可获得经验和金币奖励
"""
    
    await help_cmd.send(help_text.strip())
