"""
游戏引擎 - 统一管理游戏逻辑
"""

from typing import Dict, Any, Optional
from .user_service import UserService
from .pokemon_service import PokemonService
from .battle_service import BattleService
from .item_service import ItemService


class GameEngine:
    """游戏引擎类"""
    
    def __init__(self):
        self.user_service = UserService()
        self.pokemon_service = PokemonService()
        self.battle_service = BattleService()
        self.item_service = ItemService()
    
    async def register_user(self, qq_id: str, username: str) -> Dict[str, Any]:
        """注册用户"""
        try:
            user = await UserService.get_or_create_user(qq_id, username)
            
            # 给新用户一些初始道具
            if user.level == 1 and user.exp == 0:
                await UserService.add_item(user.id, 1, 5)  # 5个精灵球
                await UserService.add_item(user.id, 17, 3)  # 3个伤药
            
            return {
                "success": True,
                "user": user,
                "message": "注册成功！"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"注册失败：{str(e)}"
            }
    
    async def daily_catch_pokemon(self, user_id: int) -> Dict[str, Any]:
        """每日捕捉宝可梦（免费）"""
        try:
            # 这里可以实现每日免费捕捉的逻辑
            # 简化版本：直接随机给一只宝可梦
            import random
            species_id = random.randint(1, 4)
            
            # 创建一只宝可梦（不消耗精灵球）
            pokemon = await PokemonService._generate_pokemon(user_id, species_id, None)
            
            return {
                "success": True,
                "pokemon": pokemon,
                "message": "每日免费宝可梦获得！"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"获取失败：{str(e)}"
            }
    
    async def auto_heal_team(self, user_id: int) -> Dict[str, Any]:
        """自动治疗队伍（花费金币）"""
        try:
            team_pokemon = await UserService.get_user_pokemon(user_id, in_team_only=True)
            
            if not team_pokemon:
                return {"success": False, "message": "队伍中没有宝可梦"}
            
            # 计算治疗费用
            heal_cost = 0
            pokemon_to_heal = []
            
            for pokemon in team_pokemon:
                if pokemon.current_hp < pokemon.max_hp:
                    cost = (pokemon.max_hp - pokemon.current_hp) * 2  # 每点HP 2金币
                    heal_cost += cost
                    pokemon_to_heal.append(pokemon)
            
            if not pokemon_to_heal:
                return {"success": False, "message": "队伍中的宝可梦都很健康"}
            
            # 检查金币
            if not await UserService.update_user_money(user_id, -heal_cost):
                return {"success": False, "message": f"金币不足，需要 {heal_cost} 金币"}
            
            # 治疗宝可梦
            from .database import get_db_session
            async with get_db_session() as session:
                for pokemon in pokemon_to_heal:
                    pokemon.current_hp = pokemon.max_hp
                session.commit()
            
            return {
                "success": True,
                "healed_count": len(pokemon_to_heal),
                "cost": heal_cost,
                "message": f"治疗了 {len(pokemon_to_heal)} 只宝可梦，花费 {heal_cost} 金币"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"治疗失败：{str(e)}"
            }
    
    async def get_user_summary(self, qq_id: str) -> Optional[Dict[str, Any]]:
        """获取用户游戏概况"""
        try:
            user = await UserService.get_user_by_qq(qq_id)
            if not user:
                return None
            
            # 获取宝可梦信息
            all_pokemon = await UserService.get_user_pokemon(user.id)
            team_pokemon = [p for p in all_pokemon if p.in_team]
            
            # 获取道具信息
            user_items = await UserService.get_user_items(user.id)
            
            return {
                "user": user,
                "pokemon_count": len(all_pokemon),
                "team_count": len(team_pokemon),
                "item_count": len(user_items),
                "team_pokemon": team_pokemon
            }
            
        except Exception as e:
            return None
    
    async def validate_battle_readiness(self, user_id: int) -> Dict[str, Any]:
        """验证用户是否准备好对战"""
        try:
            team_pokemon = await UserService.get_user_pokemon(user_id, in_team_only=True)
            
            if not team_pokemon:
                return {
                    "ready": False,
                    "message": "队伍中没有宝可梦"
                }
            
            # 检查是否有可战斗的宝可梦
            battle_ready = [p for p in team_pokemon if p.current_hp > 0]
            
            if not battle_ready:
                return {
                    "ready": False,
                    "message": "队伍中没有可战斗的宝可梦，请先治疗"
                }
            
            return {
                "ready": True,
                "battle_pokemon_count": len(battle_ready),
                "total_pokemon_count": len(team_pokemon)
            }
            
        except Exception as e:
            return {
                "ready": False,
                "message": f"检查失败：{str(e)}"
            }
    
    async def calculate_battle_rewards(self, winner_id: int, loser_id: int, battle_type: str = "pvp") -> Dict[str, Any]:
        """计算对战奖励"""
        try:
            # 基础奖励
            base_exp = 100
            base_money = 50
            
            # 根据对战类型调整奖励
            if battle_type == "pvp":
                exp_reward = base_exp
                money_reward = base_money
            else:
                exp_reward = base_exp // 2
                money_reward = base_money // 2
            
            # 给胜者奖励
            await UserService.add_exp(winner_id, exp_reward)
            await UserService.update_user_money(winner_id, money_reward)
            
            # 给败者少量安慰奖励
            consolation_exp = exp_reward // 4
            consolation_money = money_reward // 4
            
            await UserService.add_exp(loser_id, consolation_exp)
            await UserService.update_user_money(loser_id, consolation_money)
            
            return {
                "winner_rewards": {
                    "exp": exp_reward,
                    "money": money_reward
                },
                "loser_rewards": {
                    "exp": consolation_exp,
                    "money": consolation_money
                }
            }
            
        except Exception as e:
            return {
                "error": f"计算奖励失败：{str(e)}"
            }
