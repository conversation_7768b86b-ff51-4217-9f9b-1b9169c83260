"""
宝可梦服务模块
"""

import random
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..models import UserPokemon, PokemonSpecies, User
from ..config import config
from .database import get_db_session


class PokemonService:
    """宝可梦服务类"""
    
    @staticmethod
    async def catch_pokemon(user_id: int, species_id: int, pokeball_id: int = 1) -> Dict[str, Any]:
        """捕捉宝可梦"""
        async with get_db_session() as session:
            # 获取宝可梦种族信息
            species = session.query(PokemonSpecies).filter(PokemonSpecies.id == species_id).first()
            if not species:
                return {"success": False, "message": "宝可梦种族不存在"}
            
            # 检查用户是否有足够的精灵球
            from .user_service import UserService
            if not await UserService.use_item(user_id, pokeball_id, 1):
                return {"success": False, "message": "精灵球不足"}
            
            # 计算捕获成功率
            catch_rate = PokemonService._calculate_catch_rate(species.capture_rate, pokeball_id)
            success = random.random() < catch_rate
            
            if success:
                # 生成宝可梦
                pokemon = await PokemonService._generate_pokemon(user_id, species_id, session)
                session.add(pokemon)
                session.commit()
                session.refresh(pokemon)
                
                return {
                    "success": True,
                    "pokemon": pokemon,
                    "message": f"成功捕获了{species.name_zh}！"
                }
            else:
                return {
                    "success": False,
                    "message": f"{species.name_zh}挣脱了精灵球！"
                }
    
    @staticmethod
    def _calculate_catch_rate(base_rate: int, pokeball_id: int) -> float:
        """计算捕获率"""
        # 精灵球加成
        ball_multiplier = {1: 1.0, 2: 1.5, 3: 2.0}.get(pokeball_id, 1.0)
        
        # 简化的捕获率计算
        rate = (base_rate * ball_multiplier) / 255.0
        return min(rate, 0.95)  # 最高95%成功率
    
    @staticmethod
    async def _generate_pokemon(user_id: int, species_id: int, session: Session) -> UserPokemon:
        """生成宝可梦实例"""
        species = session.query(PokemonSpecies).filter(PokemonSpecies.id == species_id).first()
        
        # 生成个体值 (0-31)
        ivs = {stat: random.randint(0, 31) for stat in 
               ['hp', 'attack', 'defense', 'sp_attack', 'sp_defense', 'speed']}
        
        # 随机性别
        gender = PokemonService._generate_gender(species.gender_rate)
        
        # 随机性格
        natures = [
            "hardy", "lonely", "brave", "adamant", "naughty",
            "bold", "docile", "relaxed", "impish", "lax",
            "timid", "hasty", "serious", "jolly", "naive",
            "modest", "mild", "quiet", "bashful", "rash",
            "calm", "gentle", "sassy", "careful", "quirky"
        ]
        nature = random.choice(natures)
        
        # 随机特性
        ability = random.choice(species.abilities) if species.abilities else "unknown"
        
        # 异色概率 (1/4096)
        is_shiny = random.randint(1, 4096) == 1
        
        pokemon = UserPokemon(
            owner_id=user_id,
            species_id=species_id,
            level=random.randint(1, 5),  # 野生宝可梦等级1-5
            gender=gender,
            nature=nature,
            ability=ability,
            iv_hp=ivs['hp'],
            iv_attack=ivs['attack'],
            iv_defense=ivs['defense'],
            iv_sp_attack=ivs['sp_attack'],
            iv_sp_defense=ivs['sp_defense'],
            iv_speed=ivs['speed'],
            is_shiny=is_shiny,
            moves=[1, 33]  # 默认技能：拍击、撞击
        )
        
        # 设置当前HP
        pokemon.current_hp = pokemon.max_hp
        
        return pokemon
    
    @staticmethod
    def _generate_gender(gender_rate: int) -> str:
        """生成性别"""
        if gender_rate == -1:
            return "genderless"
        elif gender_rate == 0:
            return "male"
        elif gender_rate == 8:
            return "female"
        else:
            # 根据性别比例随机
            return "female" if random.randint(0, 7) < gender_rate else "male"
    
    @staticmethod
    async def train_pokemon(pokemon_id: int, training_type: str = "exp") -> Dict[str, Any]:
        """训练宝可梦"""
        async with get_db_session() as session:
            pokemon = session.query(UserPokemon).filter(UserPokemon.id == pokemon_id).first()
            if not pokemon:
                return {"success": False, "message": "宝可梦不存在"}
            
            old_level = pokemon.level
            
            if training_type == "exp":
                # 增加经验值
                exp_gain = random.randint(50, 150)
                pokemon.exp += exp_gain
                
                # 检查升级
                while pokemon.exp >= PokemonService._exp_for_level(pokemon.level + 1):
                    pokemon.level += 1
                    if pokemon.level >= 100:  # 最高等级
                        pokemon.level = 100
                        break
                
                # 更新当前HP
                pokemon.current_hp = pokemon.max_hp
                
            session.commit()
            
            return {
                "success": True,
                "old_level": old_level,
                "new_level": pokemon.level,
                "level_up": pokemon.level > old_level,
                "exp_gain": exp_gain if training_type == "exp" else 0
            }
    
    @staticmethod
    def _exp_for_level(level: int) -> int:
        """计算宝可梦升级所需经验值"""
        if level <= 1:
            return 0
        return int(level ** 3)
    
    @staticmethod
    async def evolve_pokemon(pokemon_id: int) -> Dict[str, Any]:
        """进化宝可梦"""
        async with get_db_session() as session:
            pokemon = session.query(UserPokemon).filter(UserPokemon.id == pokemon_id).first()
            if not pokemon:
                return {"success": False, "message": "宝可梦不存在"}
            
            species = pokemon.species
            
            # 检查进化条件
            if not species.evolution_trigger:
                return {"success": False, "message": f"{species.name_zh}无法进化"}
            
            can_evolve = False
            
            if species.evolution_trigger == "level" and species.evolution_level:
                can_evolve = pokemon.level >= species.evolution_level
            
            if not can_evolve:
                return {"success": False, "message": "进化条件未满足"}
            
            # 查找进化后的宝可梦
            evolved_species = session.query(PokemonSpecies).filter(
                PokemonSpecies.evolves_from_species_id == species.id
            ).first()
            
            if not evolved_species:
                return {"success": False, "message": "找不到进化后的宝可梦"}
            
            # 执行进化
            old_species_name = species.name_zh
            pokemon.species_id = evolved_species.id
            pokemon.current_hp = pokemon.max_hp  # 更新HP
            
            session.commit()
            
            return {
                "success": True,
                "old_species": old_species_name,
                "new_species": evolved_species.name_zh,
                "message": f"{old_species_name}进化成了{evolved_species.name_zh}！"
            }
    
    @staticmethod
    async def add_to_team(pokemon_id: int) -> Dict[str, Any]:
        """将宝可梦加入队伍"""
        async with get_db_session() as session:
            pokemon = session.query(UserPokemon).filter(UserPokemon.id == pokemon_id).first()
            if not pokemon:
                return {"success": False, "message": "宝可梦不存在"}
            
            # 检查队伍是否已满
            team_count = session.query(UserPokemon).filter(
                and_(UserPokemon.owner_id == pokemon.owner_id, UserPokemon.in_team == True)
            ).count()
            
            if team_count >= config.pokemon_max_team_size:
                return {"success": False, "message": f"队伍已满（最多{config.pokemon_max_team_size}只）"}
            
            pokemon.in_team = True
            session.commit()
            
            return {"success": True, "message": f"{pokemon.display_name}已加入队伍"}
    
    @staticmethod
    async def remove_from_team(pokemon_id: int) -> Dict[str, Any]:
        """将宝可梦从队伍中移除"""
        async with get_db_session() as session:
            pokemon = session.query(UserPokemon).filter(UserPokemon.id == pokemon_id).first()
            if not pokemon:
                return {"success": False, "message": "宝可梦不存在"}
            
            pokemon.in_team = False
            session.commit()
            
            return {"success": True, "message": f"{pokemon.display_name}已离开队伍"}
