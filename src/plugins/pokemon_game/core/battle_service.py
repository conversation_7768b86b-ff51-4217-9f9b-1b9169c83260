"""
对战服务模块
"""

import random
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..models import Battle, BattleParticipant, BattleTurn, User, UserPokemon, BattleStatus, BattleType
from ..config import config
from .database import get_db_session


class BattleService:
    """对战服务类"""
    
    @staticmethod
    async def create_battle(challenger_id: int, defender_id: int, battle_type: BattleType = BattleType.PVP) -> Dict[str, Any]:
        """创建对战"""
        async with get_db_session() as session:
            # 检查双方是否都有可用的宝可梦
            challenger_team = session.query(UserPokemon).filter(
                and_(UserPokemon.owner_id == challenger_id, UserPokemon.in_team == True)
            ).all()
            
            defender_team = session.query(UserPokemon).filter(
                and_(UserPokemon.owner_id == defender_id, UserPokemon.in_team == True)
            ).all()
            
            if not challenger_team:
                return {"success": False, "message": "挑战者没有可用的宝可梦"}
            
            if not defender_team:
                return {"success": False, "message": "被挑战者没有可用的宝可梦"}
            
            # 创建对战
            battle = Battle(
                battle_type=battle_type,
                challenger_id=challenger_id,
                defender_id=defender_id,
                status=BattleStatus.WAITING
            )
            session.add(battle)
            session.commit()
            session.refresh(battle)
            
            # 创建参与者记录
            challenger_participant = BattleParticipant(
                battle_id=battle.id,
                user_id=challenger_id,
                team_position=1,
                is_ready=True,
                team_pokemon=[p.id for p in challenger_team]
            )
            
            defender_participant = BattleParticipant(
                battle_id=battle.id,
                user_id=defender_id,
                team_position=2,
                is_ready=False,
                team_pokemon=[p.id for p in defender_team]
            )
            
            session.add(challenger_participant)
            session.add(defender_participant)
            session.commit()
            
            return {
                "success": True,
                "battle_id": battle.id,
                "message": "对战创建成功，等待对方接受"
            }
    
    @staticmethod
    async def accept_battle(battle_id: int, user_id: int) -> Dict[str, Any]:
        """接受对战"""
        async with get_db_session() as session:
            battle = session.query(Battle).filter(Battle.id == battle_id).first()
            if not battle:
                return {"success": False, "message": "对战不存在"}
            
            if battle.status != BattleStatus.WAITING:
                return {"success": False, "message": "对战状态不正确"}
            
            if battle.defender_id != user_id:
                return {"success": False, "message": "只有被挑战者可以接受对战"}
            
            # 更新对战状态
            battle.status = BattleStatus.ACTIVE
            
            # 设置被挑战者为准备状态
            participant = session.query(BattleParticipant).filter(
                and_(BattleParticipant.battle_id == battle_id, BattleParticipant.user_id == user_id)
            ).first()
            
            if participant:
                participant.is_ready = True
            
            session.commit()
            
            return {"success": True, "message": "对战开始！"}
    
    @staticmethod
    async def get_battle_state(battle_id: int) -> Optional[Dict[str, Any]]:
        """获取对战状态"""
        async with get_db_session() as session:
            battle = session.query(Battle).filter(Battle.id == battle_id).first()
            if not battle:
                return None
            
            participants = session.query(BattleParticipant).filter(
                BattleParticipant.battle_id == battle_id
            ).all()
            
            return {
                "battle_id": battle.id,
                "status": battle.status.value,
                "current_turn": battle.current_turn,
                "participants": [
                    {
                        "user_id": p.user_id,
                        "team_position": p.team_position,
                        "is_ready": p.is_ready,
                        "active_pokemon_id": p.active_pokemon_id,
                        "pokemon_fainted": p.pokemon_fainted
                    }
                    for p in participants
                ]
            }
    
    @staticmethod
    async def use_move(battle_id: int, user_id: int, move_id: int, target_pokemon_id: Optional[int] = None) -> Dict[str, Any]:
        """使用技能"""
        async with get_db_session() as session:
            battle = session.query(Battle).filter(Battle.id == battle_id).first()
            if not battle or battle.status != BattleStatus.ACTIVE:
                return {"success": False, "message": "对战不可用"}
            
            # 获取使用者的当前宝可梦
            participant = session.query(BattleParticipant).filter(
                and_(BattleParticipant.battle_id == battle_id, BattleParticipant.user_id == user_id)
            ).first()
            
            if not participant or not participant.active_pokemon_id:
                return {"success": False, "message": "没有出战的宝可梦"}
            
            attacker = session.query(UserPokemon).filter(
                UserPokemon.id == participant.active_pokemon_id
            ).first()
            
            if not attacker or attacker.current_hp <= 0:
                return {"success": False, "message": "宝可梦无法战斗"}
            
            # 获取目标宝可梦
            if not target_pokemon_id:
                # 自动选择对手的当前宝可梦
                opponent_participant = session.query(BattleParticipant).filter(
                    and_(BattleParticipant.battle_id == battle_id, BattleParticipant.user_id != user_id)
                ).first()
                target_pokemon_id = opponent_participant.active_pokemon_id if opponent_participant else None
            
            if not target_pokemon_id:
                return {"success": False, "message": "没有有效目标"}
            
            target = session.query(UserPokemon).filter(UserPokemon.id == target_pokemon_id).first()
            if not target:
                return {"success": False, "message": "目标宝可梦不存在"}
            
            # 计算伤害
            damage_result = BattleService._calculate_damage(attacker, target, move_id, session)
            
            # 应用伤害
            target.current_hp = max(0, target.current_hp - damage_result["damage"])
            
            # 记录回合
            turn = BattleTurn(
                battle_id=battle_id,
                turn_number=battle.current_turn,
                user_id=user_id,
                pokemon_id=attacker.id,
                action_type="move",
                move_id=move_id,
                target_pokemon_id=target.id,
                damage_dealt=damage_result["damage"],
                critical_hit=damage_result["critical"],
                effectiveness=damage_result["effectiveness"],
                success=True,
                description=damage_result["description"]
            )
            session.add(turn)
            
            # 检查宝可梦是否倒下
            if target.current_hp <= 0:
                # 更新倒下计数
                target_participant = session.query(BattleParticipant).filter(
                    and_(BattleParticipant.battle_id == battle_id, BattleParticipant.user_id == target.owner_id)
                ).first()
                if target_participant:
                    target_participant.pokemon_fainted += 1
            
            # 更新回合数
            battle.current_turn += 1
            
            # 检查对战是否结束
            battle_result = BattleService._check_battle_end(battle_id, session)
            
            session.commit()
            
            result = {
                "success": True,
                "damage": damage_result["damage"],
                "critical": damage_result["critical"],
                "effectiveness": damage_result["effectiveness"],
                "target_fainted": target.current_hp <= 0,
                "description": damage_result["description"]
            }
            
            if battle_result:
                result.update(battle_result)
            
            return result
    
    @staticmethod
    def _calculate_damage(attacker: UserPokemon, target: UserPokemon, move_id: int, session: Session) -> Dict[str, Any]:
        """计算伤害"""
        from ..models import PokemonMove
        
        move = session.query(PokemonMove).filter(PokemonMove.id == move_id).first()
        if not move or not move.power:
            return {
                "damage": 0,
                "critical": False,
                "effectiveness": 1.0,
                "description": f"{attacker.display_name}使用了{move.name_zh if move else '未知技能'}，但没有效果！"
            }
        
        # 基础伤害计算
        level = attacker.level
        power = move.power
        
        # 攻击力和防御力
        if move.category == "physical":
            attack = BattleService._calculate_stat(attacker, "attack")
            defense = BattleService._calculate_stat(target, "defense")
        else:
            attack = BattleService._calculate_stat(attacker, "sp_attack")
            defense = BattleService._calculate_stat(target, "sp_defense")
        
        # 基础伤害公式
        damage = ((2 * level + 10) / 250) * (attack / defense) * power + 2
        
        # 暴击判定
        critical = random.randint(1, 24) == 1  # 1/24概率暴击
        if critical:
            damage *= 1.5
        
        # 属性相克 (简化版)
        effectiveness = BattleService._get_type_effectiveness(move.type_id, target)
        damage *= effectiveness
        
        # 随机因子
        damage *= random.uniform(0.85, 1.0)
        
        damage = int(damage)
        
        # 构建描述
        description = f"{attacker.display_name}使用了{move.name_zh}！"
        if critical:
            description += "击中了要害！"
        if effectiveness > 1.0:
            description += "效果拔群！"
        elif effectiveness < 1.0:
            description += "效果不理想..."
        
        return {
            "damage": damage,
            "critical": critical,
            "effectiveness": effectiveness,
            "description": description
        }
    
    @staticmethod
    def _calculate_stat(pokemon: UserPokemon, stat_name: str) -> int:
        """计算宝可梦能力值"""
        base_stat = getattr(pokemon.species, f"base_{stat_name}")
        iv = getattr(pokemon, f"iv_{stat_name}")
        ev = getattr(pokemon, f"ev_{stat_name}")
        level = pokemon.level
        
        if stat_name == "hp":
            return ((2 * base_stat + iv + ev // 4) * level // 100) + level + 10
        else:
            return ((2 * base_stat + iv + ev // 4) * level // 100) + 5
    
    @staticmethod
    def _get_type_effectiveness(move_type_id: int, target: UserPokemon) -> float:
        """获取属性相克倍率 (简化版)"""
        # 这里应该实现完整的属性相克表，现在只是简化版
        target_types = [t.id for t in target.species.types]
        
        # 简化的相克关系
        effectiveness_chart = {
            2: {5: 2.0, 6: 0.5, 3: 0.5},  # 火克草，被冰水克制
            3: {2: 2.0, 9: 2.0, 5: 0.5},  # 水克火地，被草克制
            5: {3: 2.0, 9: 2.0, 2: 0.5},  # 草克水地，被火克制
            4: {3: 2.0, 10: 2.0, 9: 0.5}, # 电克水飞，被地克制
        }
        
        total_effectiveness = 1.0
        for target_type in target_types:
            if move_type_id in effectiveness_chart:
                multiplier = effectiveness_chart[move_type_id].get(target_type, 1.0)
                total_effectiveness *= multiplier
        
        return total_effectiveness
    
    @staticmethod
    def _check_battle_end(battle_id: int, session: Session) -> Optional[Dict[str, Any]]:
        """检查对战是否结束"""
        participants = session.query(BattleParticipant).filter(
            BattleParticipant.battle_id == battle_id
        ).all()
        
        for participant in participants:
            # 检查是否所有宝可梦都倒下了
            team_pokemon = session.query(UserPokemon).filter(
                UserPokemon.id.in_(participant.team_pokemon)
            ).all()
            
            alive_count = sum(1 for p in team_pokemon if p.current_hp > 0)
            
            if alive_count == 0:
                # 该玩家败北
                battle = session.query(Battle).filter(Battle.id == battle_id).first()
                battle.status = BattleStatus.FINISHED
                
                # 确定胜者
                winner_participant = next(p for p in participants if p.user_id != participant.user_id)
                battle.winner_id = winner_participant.user_id
                
                return {
                    "battle_ended": True,
                    "winner_id": winner_participant.user_id,
                    "loser_id": participant.user_id
                }
        
        return None
