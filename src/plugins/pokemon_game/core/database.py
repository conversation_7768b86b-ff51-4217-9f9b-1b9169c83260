"""
数据库初始化和管理
"""

import asyncio
from contextlib import asynccontextmanager
from typing import AsyncGenerator
from sqlalchemy.orm import Session

from ..models import Base, engine, SessionLocal, get_session
from ..models.base import create_tables
from .data_loader import DataLoader


def init_database():
    """初始化数据库"""
    try:
        # 创建所有表
        create_tables()
        
        # 加载基础数据
        asyncio.create_task(load_base_data())
        
        print("数据库初始化完成")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        raise


async def load_base_data():
    """加载基础游戏数据"""
    try:
        loader = DataLoader()
        
        async with get_db_session() as session:
            # 检查是否已有数据
            from ..models import PokemonType
            if session.query(PokemonType).count() == 0:
                await loader.load_types(session)
                await loader.load_pokemon_species(session)
                await loader.load_moves(session)
                await loader.load_items(session)
                
                print("基础游戏数据加载完成")
            else:
                print("基础数据已存在，跳过加载")
                
    except Exception as e:
        print(f"基础数据加载失败: {e}")


@asynccontextmanager
async def get_db_session() -> AsyncGenerator[Session, None]:
    """获取数据库会话的异步上下文管理器"""
    async with get_session() as session:
        yield session
