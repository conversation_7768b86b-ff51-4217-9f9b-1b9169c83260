"""
游戏基础数据加载器
"""

from sqlalchemy.orm import Session
from typing import Dict, List, Any

from ..models import PokemonType, PokemonSpecies, PokemonMove, Item, ItemCategory


class DataLoader:
    """游戏数据加载器"""
    
    async def load_types(self, session: Session):
        """加载宝可梦属性类型"""
        types_data = [
            {"id": 1, "name": "normal", "name_zh": "一般", "color": "#A8A878"},
            {"id": 2, "name": "fire", "name_zh": "火", "color": "#F08030"},
            {"id": 3, "name": "water", "name_zh": "水", "color": "#6890F0"},
            {"id": 4, "name": "electric", "name_zh": "电", "color": "#F8D030"},
            {"id": 5, "name": "grass", "name_zh": "草", "color": "#78C850"},
            {"id": 6, "name": "ice", "name_zh": "冰", "color": "#98D8D8"},
            {"id": 7, "name": "fighting", "name_zh": "格斗", "color": "#C03028"},
            {"id": 8, "name": "poison", "name_zh": "毒", "color": "#A040A0"},
            {"id": 9, "name": "ground", "name_zh": "地面", "color": "#E0C068"},
            {"id": 10, "name": "flying", "name_zh": "飞行", "color": "#A890F0"},
            {"id": 11, "name": "psychic", "name_zh": "超能力", "color": "#F85888"},
            {"id": 12, "name": "bug", "name_zh": "虫", "color": "#A8B820"},
            {"id": 13, "name": "rock", "name_zh": "岩石", "color": "#B8A038"},
            {"id": 14, "name": "ghost", "name_zh": "幽灵", "color": "#705898"},
            {"id": 15, "name": "dragon", "name_zh": "龙", "color": "#7038F8"},
            {"id": 16, "name": "dark", "name_zh": "恶", "color": "#705848"},
            {"id": 17, "name": "steel", "name_zh": "钢", "color": "#B8B8D0"},
            {"id": 18, "name": "fairy", "name_zh": "妖精", "color": "#EE99AC"},
        ]
        
        for type_data in types_data:
            pokemon_type = PokemonType(**type_data)
            session.add(pokemon_type)
        
        session.commit()
    
    async def load_pokemon_species(self, session: Session):
        """加载宝可梦种族数据 (示例数据)"""
        # 这里只加载几个示例宝可梦，实际应该从完整的数据源加载
        species_data = [
            {
                "id": 1, "name": "bulbasaur", "name_zh": "妙蛙种子",
                "base_hp": 45, "base_attack": 49, "base_defense": 49,
                "base_sp_attack": 65, "base_sp_defense": 65, "base_speed": 45,
                "height": 0.7, "weight": 6.9, "capture_rate": 45,
                "abilities": ["overgrow"], "hidden_ability": "chlorophyll",
                "generation": 1
            },
            {
                "id": 4, "name": "charmander", "name_zh": "小火龙", 
                "base_hp": 39, "base_attack": 52, "base_defense": 43,
                "base_sp_attack": 60, "base_sp_defense": 50, "base_speed": 65,
                "height": 0.6, "weight": 8.5, "capture_rate": 45,
                "abilities": ["blaze"], "hidden_ability": "solar-power",
                "generation": 1
            },
            {
                "id": 7, "name": "squirtle", "name_zh": "杰尼龟",
                "base_hp": 44, "base_attack": 48, "base_defense": 65,
                "base_sp_attack": 50, "base_sp_defense": 64, "base_speed": 43,
                "height": 0.5, "weight": 9.0, "capture_rate": 45,
                "abilities": ["torrent"], "hidden_ability": "rain-dish",
                "generation": 1
            },
            {
                "id": 25, "name": "pikachu", "name_zh": "皮卡丘",
                "base_hp": 35, "base_attack": 55, "base_defense": 40,
                "base_sp_attack": 50, "base_sp_defense": 50, "base_speed": 90,
                "height": 0.4, "weight": 6.0, "capture_rate": 190,
                "abilities": ["static"], "hidden_ability": "lightning-rod",
                "generation": 1
            },
        ]
        
        for species in species_data:
            pokemon_species = PokemonSpecies(**species)
            session.add(pokemon_species)
        
        session.commit()
    
    async def load_moves(self, session: Session):
        """加载宝可梦技能数据 (示例数据)"""
        moves_data = [
            {
                "id": 1, "name": "pound", "name_zh": "拍击",
                "type_id": 1, "category": "physical", "power": 40,
                "accuracy": 100, "pp": 35, "priority": 0,
                "description": "用前脚或尾巴等拍打对手进行攻击。"
            },
            {
                "id": 33, "name": "tackle", "name_zh": "撞击",
                "type_id": 1, "category": "physical", "power": 40,
                "accuracy": 100, "pp": 35, "priority": 0,
                "description": "用整个身体撞向对手进行攻击。"
            },
            {
                "id": 52, "name": "ember", "name_zh": "火花",
                "type_id": 2, "category": "special", "power": 40,
                "accuracy": 100, "pp": 25, "priority": 0,
                "description": "向对手发射小火焰进行攻击。有时会让对手陷入灼伤状态。"
            },
            {
                "id": 55, "name": "water-gun", "name_zh": "水枪",
                "type_id": 3, "category": "special", "power": 40,
                "accuracy": 100, "pp": 25, "priority": 0,
                "description": "向对手猛烈地喷射水流进行攻击。"
            },
            {
                "id": 84, "name": "thundershock", "name_zh": "电击",
                "type_id": 4, "category": "special", "power": 40,
                "accuracy": 100, "pp": 30, "priority": 0,
                "description": "发出电流攻击对手。有时会让对手陷入麻痹状态。"
            },
        ]
        
        for move_data in moves_data:
            move = PokemonMove(**move_data)
            session.add(move)
        
        session.commit()
    
    async def load_items(self, session: Session):
        """加载道具数据 (示例数据)"""
        items_data = [
            {
                "id": 1, "name": "poke-ball", "name_zh": "精灵球",
                "category": ItemCategory.POKEBALL, "buy_price": 200, "sell_price": 100,
                "pokeball_catch_rate": 1, "description": "用于捕捉野生宝可梦的球。"
            },
            {
                "id": 2, "name": "great-ball", "name_zh": "超级球",
                "category": ItemCategory.POKEBALL, "buy_price": 600, "sell_price": 300,
                "pokeball_catch_rate": 2, "description": "性能比精灵球更好的球。"
            },
            {
                "id": 3, "name": "ultra-ball", "name_zh": "高级球",
                "category": ItemCategory.POKEBALL, "buy_price": 1200, "sell_price": 600,
                "pokeball_catch_rate": 3, "description": "性能非常好的球。"
            },
            {
                "id": 17, "name": "potion", "name_zh": "伤药",
                "category": ItemCategory.MEDICINE, "buy_price": 300, "sell_price": 150,
                "medicine_heal_amount": 20, "usable_on_pokemon": True,
                "description": "喷雾式药剂。能让宝可梦回复20HP。"
            },
            {
                "id": 18, "name": "super-potion", "name_zh": "好伤药",
                "category": ItemCategory.MEDICINE, "buy_price": 700, "sell_price": 350,
                "medicine_heal_amount": 50, "usable_on_pokemon": True,
                "description": "喷雾式药剂。能让宝可梦回复50HP。"
            },
        ]
        
        for item_data in items_data:
            item = Item(**item_data)
            session.add(item)
        
        session.commit()
