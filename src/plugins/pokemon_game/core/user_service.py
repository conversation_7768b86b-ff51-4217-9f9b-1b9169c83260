"""
用户服务模块
"""

from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..models import User, UserPokemon, UserItem
from .database import get_db_session


class UserService:
    """用户服务类"""
    
    @staticmethod
    async def get_or_create_user(qq_id: str, username: str) -> User:
        """获取或创建用户"""
        async with get_db_session() as session:
            user = session.query(User).filter(User.qq_id == qq_id).first()
            
            if not user:
                user = User(
                    qq_id=qq_id,
                    username=username,
                    level=1,
                    exp=0,
                    money=1000
                )
                session.add(user)
                session.commit()
                session.refresh(user)
            
            return user
    
    @staticmethod
    async def get_user_by_qq(qq_id: str) -> Optional[User]:
        """根据QQ号获取用户"""
        async with get_db_session() as session:
            return session.query(User).filter(User.qq_id == qq_id).first()
    
    @staticmethod
    async def update_user_money(user_id: int, amount: int) -> bool:
        """更新用户金钱"""
        async with get_db_session() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if user and user.money + amount >= 0:
                user.money += amount
                session.commit()
                return True
            return False
    
    @staticmethod
    async def add_exp(user_id: int, exp: int) -> dict:
        """增加经验值"""
        async with get_db_session() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return {"success": False, "message": "用户不存在"}
            
            old_level = user.level
            user.exp += exp
            
            # 计算等级提升
            level_up = False
            while user.exp >= UserService._exp_for_level(user.level + 1):
                user.level += 1
                level_up = True
            
            session.commit()
            
            return {
                "success": True,
                "old_level": old_level,
                "new_level": user.level,
                "level_up": level_up,
                "total_exp": user.exp
            }
    
    @staticmethod
    def _exp_for_level(level: int) -> int:
        """计算升级所需经验值"""
        if level <= 1:
            return 0
        return int(100 * (level ** 1.5))
    
    @staticmethod
    async def get_user_pokemon(user_id: int, in_team_only: bool = False) -> List[UserPokemon]:
        """获取用户的宝可梦"""
        async with get_db_session() as session:
            query = session.query(UserPokemon).filter(UserPokemon.owner_id == user_id)
            
            if in_team_only:
                query = query.filter(UserPokemon.in_team == True)
            
            return query.all()
    
    @staticmethod
    async def get_user_items(user_id: int) -> List[UserItem]:
        """获取用户的道具"""
        async with get_db_session() as session:
            return session.query(UserItem).filter(UserItem.owner_id == user_id).all()
    
    @staticmethod
    async def add_item(user_id: int, item_id: int, quantity: int = 1) -> bool:
        """给用户添加道具"""
        async with get_db_session() as session:
            # 检查是否已有该道具
            user_item = session.query(UserItem).filter(
                and_(UserItem.owner_id == user_id, UserItem.item_id == item_id)
            ).first()
            
            if user_item:
                user_item.quantity += quantity
            else:
                user_item = UserItem(
                    owner_id=user_id,
                    item_id=item_id,
                    quantity=quantity
                )
                session.add(user_item)
            
            session.commit()
            return True
    
    @staticmethod
    async def use_item(user_id: int, item_id: int, quantity: int = 1) -> bool:
        """使用道具"""
        async with get_db_session() as session:
            user_item = session.query(UserItem).filter(
                and_(UserItem.owner_id == user_id, UserItem.item_id == item_id)
            ).first()
            
            if user_item and user_item.quantity >= quantity:
                user_item.quantity -= quantity
                if user_item.quantity <= 0:
                    session.delete(user_item)
                session.commit()
                return True
            
            return False
    
    @staticmethod
    async def update_battle_stats(user_id: int, won: bool):
        """更新对战统计"""
        async with get_db_session() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if user:
                user.total_battles += 1
                if won:
                    user.wins += 1
                else:
                    user.losses += 1
                session.commit()
