"""
道具服务模块
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from ..models import Item, UserItem, UserPokemon, ItemCategory
from .database import get_db_session
from .user_service import UserService


class ItemService:
    """道具服务类"""
    
    @staticmethod
    async def get_shop_items() -> List[Item]:
        """获取商店道具列表"""
        async with get_db_session() as session:
            # 只显示可购买的道具
            return session.query(Item).filter(
                and_(Item.buy_price > 0, Item.is_tradeable == True)
            ).all()
    
    @staticmethod
    async def buy_item(user_id: int, item_name: str, quantity: int = 1) -> Dict[str, Any]:
        """购买道具"""
        async with get_db_session() as session:
            # 查找道具
            item = session.query(Item).filter(Item.name_zh == item_name).first()
            if not item:
                return {"success": False, "message": f"找不到道具：{item_name}"}
            
            if item.buy_price <= 0:
                return {"success": False, "message": "该道具不可购买"}
            
            total_cost = item.buy_price * quantity
            
            # 检查用户金币
            from ..models import User
            user = session.query(User).filter(User.id == user_id).first()
            if not user:
                return {"success": False, "message": "用户不存在"}
            
            if user.money < total_cost:
                return {"success": False, "message": f"金币不足。需要 {total_cost}，您有 {user.money}"}
            
            # 扣除金币
            user.money -= total_cost
            
            # 添加道具
            await UserService.add_item(user_id, item.id, quantity)
            
            session.commit()
            
            return {
                "success": True,
                "item_name": item.name_zh,
                "quantity": quantity,
                "cost": total_cost,
                "remaining_money": user.money
            }
    
    @staticmethod
    async def use_item_on_pokemon(user_id: int, item_name: str, pokemon_id: int) -> Dict[str, Any]:
        """对宝可梦使用道具"""
        async with get_db_session() as session:
            # 查找道具
            item = session.query(Item).filter(Item.name_zh == item_name).first()
            if not item:
                return {"success": False, "message": f"找不到道具：{item_name}"}
            
            if not item.usable_on_pokemon:
                return {"success": False, "message": f"{item_name}不能对宝可梦使用"}
            
            # 检查用户是否有该道具
            user_item = session.query(UserItem).filter(
                and_(UserItem.owner_id == user_id, UserItem.item_id == item.id)
            ).first()
            
            if not user_item or user_item.quantity <= 0:
                return {"success": False, "message": f"您没有{item_name}"}
            
            # 检查宝可梦是否属于用户
            pokemon = session.query(UserPokemon).filter(
                and_(UserPokemon.id == pokemon_id, UserPokemon.owner_id == user_id)
            ).first()
            
            if not pokemon:
                return {"success": False, "message": "找不到指定的宝可梦"}
            
            # 根据道具类型执行效果
            result = ItemService._apply_item_effect(item, pokemon, session)
            
            if result["success"]:
                # 消耗道具
                user_item.quantity -= 1
                if user_item.quantity <= 0:
                    session.delete(user_item)
                
                session.commit()
                
                result.update({
                    "item_name": item.name_zh,
                    "pokemon_name": pokemon.display_name
                })
            
            return result
    
    @staticmethod
    def _apply_item_effect(item: Item, pokemon: UserPokemon, session: Session) -> Dict[str, Any]:
        """应用道具效果"""
        if item.category == ItemCategory.MEDICINE:
            # 药品类道具
            if item.medicine_heal_amount:
                old_hp = pokemon.current_hp
                max_hp = pokemon.max_hp
                
                if old_hp >= max_hp:
                    return {"success": False, "message": f"{pokemon.display_name}的HP已经满了"}
                
                # 回复HP
                heal_amount = min(item.medicine_heal_amount, max_hp - old_hp)
                pokemon.current_hp += heal_amount
                
                return {
                    "success": True,
                    "effect": "heal",
                    "heal_amount": heal_amount,
                    "old_hp": old_hp,
                    "new_hp": pokemon.current_hp,
                    "max_hp": max_hp
                }
        
        elif item.category == ItemCategory.EVOLUTION:
            # 进化道具
            species = pokemon.species
            if (species.evolution_trigger == "stone" and 
                species.evolution_item_id == item.id):
                
                # 查找进化后的宝可梦
                from ..models import PokemonSpecies
                evolved_species = session.query(PokemonSpecies).filter(
                    PokemonSpecies.evolves_from_species_id == species.id
                ).first()
                
                if evolved_species:
                    old_species_name = species.name_zh
                    pokemon.species_id = evolved_species.id
                    pokemon.current_hp = pokemon.max_hp  # 更新HP
                    
                    return {
                        "success": True,
                        "effect": "evolution",
                        "old_species": old_species_name,
                        "new_species": evolved_species.name_zh
                    }
                else:
                    return {"success": False, "message": "进化失败"}
            else:
                return {"success": False, "message": f"{pokemon.display_name}不能使用{item.name_zh}进化"}
        
        # 其他道具类型
        return {"success": False, "message": "该道具的效果尚未实现"}
    
    @staticmethod
    async def get_item_by_name(item_name: str) -> Optional[Item]:
        """根据名称获取道具"""
        async with get_db_session() as session:
            return session.query(Item).filter(Item.name_zh == item_name).first()
    
    @staticmethod
    async def get_user_item_count(user_id: int, item_id: int) -> int:
        """获取用户拥有的道具数量"""
        async with get_db_session() as session:
            user_item = session.query(UserItem).filter(
                and_(UserItem.owner_id == user_id, UserItem.item_id == item_id)
            ).first()
            
            return user_item.quantity if user_item else 0
