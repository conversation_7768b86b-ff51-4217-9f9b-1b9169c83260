"""
宝可梦游戏插件

一个基于NoneBot2的宝可梦游戏插件，实现局外养成和局内PVP对战功能。
基于第九世代（朱紫）的游戏内容。
"""

from nonebot.plugin import PluginMetadata

from . import commands
from .core import init_database

__plugin_meta__ = PluginMetadata(
    name="宝可梦游戏",
    description="一个完整的宝可梦游戏体验，包含养成和对战功能",
    usage="""
基础命令：
- /pokemon help - 查看帮助
- /pokemon register - 注册游戏账号
- /pokemon profile - 查看个人信息
- /pokemon team - 查看队伍

养成系统：
- /pokemon catch - 捕捉宝可梦
- /pokemon train <宝可梦ID> - 训练宝可梦
- /pokemon evolve <宝可梦ID> - 进化宝可梦
- /pokemon items - 查看道具

对战系统：
- /pokemon battle <@用户> - 发起PVP对战
- /pokemon battle accept - 接受对战
- /pokemon battle decline - 拒绝对战
""",
    type="application",
    homepage="https://github.com/your-repo/pokemon-game",
    config=None,
    supported_adapters={"~onebot.v11"},
)

# 初始化数据库
init_database()
