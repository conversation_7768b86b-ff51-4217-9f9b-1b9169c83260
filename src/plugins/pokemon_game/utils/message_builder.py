"""
消息构建器
"""

from typing import List, Dict, Any
from ..models import User, UserPokemon


class MessageBuilder:
    """消息构建器类"""
    
    @staticmethod
    def build_register_success(user: User) -> str:
        """构建注册成功消息"""
        return f"""
🎉 注册成功！

👤 训练师信息：
• 姓名：{user.username}
• 等级：{user.level}
• 金币：{user.money}

🎮 开始您的宝可梦冒险吧！
使用 /pokemon catch 捕捉您的第一只宝可梦！
""".strip()
    
    @staticmethod
    def build_profile(user: User, team_pokemon: List[UserPokemon], total_pokemon: int) -> str:
        """构建个人信息消息"""
        win_rate = f"{user.win_rate:.1%}" if user.total_battles > 0 else "暂无"
        
        message = f"""
👤 训练师档案

📊 基本信息：
• 姓名：{user.username}
• 等级：{user.level} (经验值：{user.exp})
• 金币：{user.money}

⚔️ 对战记录：
• 总场次：{user.total_battles}
• 胜利：{user.wins} 场
• 失败：{user.losses} 场
• 胜率：{win_rate}

🎒 宝可梦：
• 队伍：{len(team_pokemon)}/6
• 总数：{total_pokemon}
""".strip()
        
        if team_pokemon:
            message += "\n\n🔥 当前队伍："
            for i, pokemon in enumerate(team_pokemon, 1):
                status = "💚" if pokemon.current_hp > pokemon.max_hp * 0.7 else "💛" if pokemon.current_hp > pokemon.max_hp * 0.3 else "❤️"
                shiny = "✨" if pokemon.is_shiny else ""
                message += f"\n{i}. {shiny}{pokemon.display_name} Lv.{pokemon.level} {status}"
        
        return message
    
    @staticmethod
    def build_catch_success(pokemon: UserPokemon) -> str:
        """构建捕捉成功消息"""
        shiny_text = "✨异色✨" if pokemon.is_shiny else ""
        
        return f"""
🎉 捕捉成功！

{shiny_text}
📱 宝可梦信息：
• 名称：{pokemon.species.name_zh}
• 等级：{pokemon.level}
• 性别：{pokemon.gender}
• 性格：{pokemon.nature}
• 特性：{pokemon.ability}

💪 个体值：
• HP：{pokemon.iv_hp}/31
• 攻击：{pokemon.iv_attack}/31
• 防御：{pokemon.iv_defense}/31
• 特攻：{pokemon.iv_sp_attack}/31
• 特防：{pokemon.iv_sp_defense}/31
• 速度：{pokemon.iv_speed}/31

使用 /pokemon add {pokemon.id} 将其加入队伍！
""".strip()
    
    @staticmethod
    def build_train_result(pokemon: UserPokemon, result: Dict[str, Any]) -> str:
        """构建训练结果消息"""
        message = f"🏋️ 训练完成！\n\n{pokemon.display_name} 获得了 {result['exp_gain']} 经验值！"
        
        if result["level_up"]:
            message += f"\n\n🎉 恭喜！{pokemon.display_name} 升级了！"
            message += f"\nLv.{result['old_level']} → Lv.{result['new_level']}"
        
        return message
    
    @staticmethod
    def build_evolution_result(result: Dict[str, Any]) -> str:
        """构建进化结果消息"""
        return f"""
🌟 进化成功！

{result['old_species']} 进化成了 {result['new_species']}！

✨ 恭喜您的宝可梦获得了新的力量！
""".strip()
    
    @staticmethod
    def build_team_info(team_pokemon: List[UserPokemon]) -> str:
        """构建队伍信息消息"""
        message = f"⚔️ 您的队伍 ({len(team_pokemon)}/6)\n"
        
        for i, pokemon in enumerate(team_pokemon, 1):
            hp_percent = pokemon.current_hp / pokemon.max_hp
            status = "💚" if hp_percent > 0.7 else "💛" if hp_percent > 0.3 else "❤️"
            shiny = "✨" if pokemon.is_shiny else ""
            
            message += f"\n{i}. {shiny}{pokemon.display_name}"
            message += f"\n   Lv.{pokemon.level} | HP: {pokemon.current_hp}/{pokemon.max_hp} {status}"
            message += f"\n   类型：{' / '.join(pokemon.species.type_names)}"
        
        return message
    
    @staticmethod
    def build_box_info(all_pokemon: List[UserPokemon]) -> str:
        """构建盒子信息消息"""
        team_pokemon = [p for p in all_pokemon if p.in_team]
        box_pokemon = [p for p in all_pokemon if not p.in_team]
        
        message = f"📦 宝可梦盒子 (总计：{len(all_pokemon)})\n"
        
        if team_pokemon:
            message += f"\n⚔️ 队伍中 ({len(team_pokemon)})："
            for pokemon in team_pokemon:
                shiny = "✨" if pokemon.is_shiny else ""
                message += f"\n• ID:{pokemon.id} {shiny}{pokemon.display_name} Lv.{pokemon.level}"
        
        if box_pokemon:
            message += f"\n\n📦 盒子中 ({len(box_pokemon)})："
            for pokemon in box_pokemon[:10]:  # 只显示前10个
                shiny = "✨" if pokemon.is_shiny else ""
                message += f"\n• ID:{pokemon.id} {shiny}{pokemon.display_name} Lv.{pokemon.level}"
            
            if len(box_pokemon) > 10:
                message += f"\n... 还有 {len(box_pokemon) - 10} 只宝可梦"
        
        return message
    
    @staticmethod
    def build_battle_invitation(challenger: str, defender: str) -> str:
        """构建对战邀请消息"""
        return f"""
⚔️ 对战邀请

{challenger} 向 {defender} 发起了宝可梦对战！

{defender} 请选择：
• /pokemon accept - 接受对战
• /pokemon decline - 拒绝对战

⏰ 请在60秒内回应，否则邀请将自动取消。
""".strip()
    
    @staticmethod
    def build_battle_start() -> str:
        """构建对战开始消息"""
        return """
🔥 对战开始！

双方训练师请准备好您的宝可梦！
使用 /pokemon move <技能名> 来攻击对手！

可用技能：撞击、拍击、火花、水枪、电击
""".strip()
    
    @staticmethod
    def build_move_result(result: Dict[str, Any]) -> str:
        """构建技能使用结果消息"""
        message = result["description"]
        
        if result["damage"] > 0:
            message += f"\n造成了 {result['damage']} 点伤害！"
        
        if result.get("target_fainted"):
            message += "\n对手的宝可梦倒下了！"
        
        return message
    
    @staticmethod
    def build_battle_end(winner_id: int, loser_id: int) -> str:
        """构建对战结束消息"""
        return f"""
🏆 对战结束！

胜利者：用户 {winner_id}
失败者：用户 {loser_id}

🎉 恭喜获胜的训练师！
💰 胜利者获得了经验值和金币奖励！
""".strip()
    
    @staticmethod
    def build_battle_status(battle_state: Dict[str, Any]) -> str:
        """构建对战状态消息"""
        message = f"⚔️ 对战状态\n\n回合：{battle_state['current_turn']}\n"

        for participant in battle_state["participants"]:
            message += f"\n玩家 {participant['user_id']}："
            message += f"\n• 倒下的宝可梦：{participant['pokemon_fainted']}"
            if participant["active_pokemon_id"]:
                message += f"\n• 当前宝可梦：ID {participant['active_pokemon_id']}"

        return message

    @staticmethod
    def build_items_info(user_items: List) -> str:
        """构建道具信息消息"""
        from ..models import UserItem

        message = "🎒 您的背包\n"

        # 按类别分组
        categories = {}
        for user_item in user_items:
            category = user_item.item.category.value
            if category not in categories:
                categories[category] = []
            categories[category].append(user_item)

        category_names = {
            "pokeball": "🥎 精灵球",
            "medicine": "💊 药品",
            "berry": "🍓 树果",
            "tm": "💿 技能机器",
            "evolution": "🔮 进化道具",
            "battle_item": "⚔️ 对战道具",
            "held_item": "🎁 携带道具",
            "key_item": "🗝️ 重要道具",
            "misc": "📦 其他"
        }

        for category, items in categories.items():
            category_name = category_names.get(category, category)
            message += f"\n{category_name}："
            for user_item in items:
                message += f"\n• {user_item.item.name_zh} x{user_item.quantity}"

        return message

    @staticmethod
    def build_shop_info(shop_items: List) -> str:
        """构建商店信息消息"""
        from ..models import Item

        message = "🏪 宝可梦商店\n\n💰 可购买道具：\n"

        for item in shop_items:
            message += f"\n• {item.name_zh} - {item.buy_price}金币"
            if item.description:
                message += f"\n  {item.description[:30]}..."

        message += "\n\n💡 使用 /pokemon buy <道具名> [数量] 购买道具"

        return message

    @staticmethod
    def build_buy_success(result: Dict[str, Any]) -> str:
        """构建购买成功消息"""
        return f"""
✅ 购买成功！

📦 获得道具：{result['item_name']} x{result['quantity']}
💰 花费金币：{result['cost']}
💳 剩余金币：{result['remaining_money']}
""".strip()

    @staticmethod
    def build_use_item_result(result: Dict[str, Any]) -> str:
        """构建使用道具结果消息"""
        message = f"✨ 使用了{result['item_name']}！\n"

        if result["effect"] == "heal":
            message += f"\n{result['pokemon_name']} 回复了 {result['heal_amount']} HP！"
            message += f"\nHP: {result['old_hp']} → {result['new_hp']}/{result['max_hp']}"

        elif result["effect"] == "evolution":
            message += f"\n🌟 {result['pokemon_name']} 进化成了 {result['new_species']}！"

        return message
