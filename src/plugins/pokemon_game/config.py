"""
配置文件
"""

from typing import Optional
from pydantic import BaseModel
from nonebot import get_driver


class Config(BaseModel):
    """插件配置"""
    
    # 数据库配置
    pokemon_db_url: str = "sqlite:///pokemon_game.db"
    
    # 游戏配置
    pokemon_max_team_size: int = 6
    pokemon_max_box_size: int = 720  # 24盒 * 30只
    pokemon_daily_catch_limit: int = 10
    pokemon_exp_multiplier: float = 1.0
    
    # 对战配置
    pokemon_battle_timeout: int = 300  # 5分钟
    pokemon_turn_timeout: int = 60     # 1分钟
    
    # 图片配置
    pokemon_image_cache_dir: str = "data/pokemon/images"
    pokemon_sprite_base_url: str = "https://raw.githubusercontent.com/PokeAPI/sprites/master/sprites/pokemon/"
    
    # 调试配置
    pokemon_debug: bool = False


# 获取配置实例
driver = get_driver()
config = Config.parse_obj(driver.config.dict())
