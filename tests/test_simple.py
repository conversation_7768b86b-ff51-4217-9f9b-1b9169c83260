"""
简化的宝可梦游戏插件测试
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_imports():
    """测试基本导入"""
    try:
        # 测试模型导入
        from src.plugins.pokemon_game.models.user import User, UserPokemon
        from src.plugins.pokemon_game.models.pokemon import PokemonSpecies, PokemonMove
        from src.plugins.pokemon_game.models.battle import Battle, BattleStatus
        from src.plugins.pokemon_game.models.item import Item, ItemCategory
        
        print("✅ 模型导入成功")
        
        # 测试工具导入
        from src.plugins.pokemon_game.utils.message_builder import MessageBuilder
        
        print("✅ 工具导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_message_builder():
    """测试消息构建器"""
    try:
        from src.plugins.pokemon_game.utils.message_builder import MessageBuilder
        
        # 创建模拟数据
        class MockUser:
            def __init__(self):
                self.qq_id = "123456"
                self.username = "测试用户"
                self.level = 5
                self.exp = 500
                self.money = 2000
                self.total_battles = 10
                self.wins = 7
                self.losses = 3
                
            @property
            def win_rate(self):
                return self.wins / self.total_battles if self.total_battles > 0 else 0
        
        user = MockUser()
        
        # 测试个人信息消息构建
        message = MessageBuilder.build_profile(user, [], 5)
        assert "测试用户" in message
        assert "等级：5" in message
        assert "金币：2000" in message
        
        print("✅ 消息构建器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 消息构建器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_structures():
    """测试数据结构"""
    try:
        from src.plugins.pokemon_game.models.battle import BattleStatus, BattleType
        from src.plugins.pokemon_game.models.item import ItemCategory
        
        # 测试枚举
        assert BattleStatus.WAITING.value == "waiting"
        assert BattleType.PVP.value == "pvp"
        assert ItemCategory.POKEBALL.value == "pokeball"
        
        print("✅ 数据结构测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("🧪 开始简化测试...")
    
    tests = [
        test_imports,
        test_message_builder,
        test_data_structures,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️ 部分测试失败")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
